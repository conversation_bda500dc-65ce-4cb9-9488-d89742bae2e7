// /var/www/nirvana-backend/ecosystem.config.production.js
module.exports = {
  apps: [
    {
      name: 'nirvana-backend', // <--- MAKE SURE THIS IS SET TO 'nirvana-backend'
      script: './server/index.js',
      cwd: '/var/www/nirvana-backend', // <--- AND THIS IS SET CORRECTLY
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production', // Consider changing this from 'testing' if this is your production setup
        PORT: 5000
      },
      env_file: '.env',
      // ... rest of your config
    }
  ]
};