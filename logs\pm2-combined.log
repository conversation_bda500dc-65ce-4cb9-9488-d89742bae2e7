/var/www/nirvana-backend/server/index.js:76
const http = require('http');
      ^

SyntaxError: Identifier 'http' has already been declared
    at internalCompileFunction (node:internal/vm:76:18)
    at wrapSafe (node:internal/modules/cjs/loader:1283:20)
    at Module._compile (node:internal/modules/cjs/loader:1328:27)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1019:12)
    at /usr/lib/node_modules/pm2/lib/ProcessContainer.js:292:27
    at wrapper (/usr/lib/node_modules/pm2/node_modules/async/internal/once.js:12:16)
    at next (/usr/lib/node_modules/pm2/node_modules/async/waterfall.js:96:20)
    at /usr/lib/node_modules/pm2/node_modules/async/internal/onlyOnce.js:12:16
/var/www/nirvana-backend/server/index.js:76
const http = require('http');
      ^

SyntaxError: Identifier 'http' has already been declared
    at internalCompileFunction (node:internal/vm:76:18)
    at wrapSafe (node:internal/modules/cjs/loader:1283:20)
    at Module._compile (node:internal/modules/cjs/loader:1328:27)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1019:12)
    at /usr/lib/node_modules/pm2/lib/ProcessContainer.js:292:27
    at wrapper (/usr/lib/node_modules/pm2/node_modules/async/internal/once.js:12:16)
    at next (/usr/lib/node_modules/pm2/node_modules/async/waterfall.js:96:20)
    at /usr/lib/node_modules/pm2/node_modules/async/internal/onlyOnce.js:12:16
/var/www/nirvana-backend/server/index.js:76
const http = require('http');
      ^

SyntaxError: Identifier 'http' has already been declared
    at internalCompileFunction (node:internal/vm:76:18)
    at wrapSafe (node:internal/modules/cjs/loader:1283:20)
    at Module._compile (node:internal/modules/cjs/loader:1328:27)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1019:12)
    at /usr/lib/node_modules/pm2/lib/ProcessContainer.js:292:27
    at wrapper (/usr/lib/node_modules/pm2/node_modules/async/internal/once.js:12:16)
    at next (/usr/lib/node_modules/pm2/node_modules/async/waterfall.js:96:20)
    at /usr/lib/node_modules/pm2/node_modules/async/internal/onlyOnce.js:12:16
/var/www/nirvana-backend/server/index.js:76
const http = require('http');
      ^

SyntaxError: Identifier 'http' has already been declared
    at internalCompileFunction (node:internal/vm:76:18)
    at wrapSafe (node:internal/modules/cjs/loader:1283:20)
    at Module._compile (node:internal/modules/cjs/loader:1328:27)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1019:12)
    at /usr/lib/node_modules/pm2/lib/ProcessContainer.js:292:27
    at wrapper (/usr/lib/node_modules/pm2/node_modules/async/internal/once.js:12:16)
    at next (/usr/lib/node_modules/pm2/node_modules/async/waterfall.js:96:20)
    at /usr/lib/node_modules/pm2/node_modules/async/internal/onlyOnce.js:12:16
2025-07-30T01:40:19: ✅ Environment validation passed
2025-07-30T01:40:29: ✅ Environment validation passed
2025-07-30T01:43:03: ✅ Environment validation passed
2025-07-30T01:43:13: ✅ Environment validation passed
2025-07-30T01:44:38: ✅ Environment validation passed
2025-07-30T01:44:48: ✅ Environment validation passed
2025-07-30T01:48:16: ✅ Environment validation passed
2025-07-30T01:48:26: ✅ Environment validation passed
2025-07-30T02:01:44: ✅ Environment validation passed
2025-07-30T02:01:45: ✅ Environment validation passed
2025-07-30T02:01:46: ✅ Environment validation passed
2025-07-30T02:01:48: ✅ Environment validation passed
2025-07-30T02:01:49: ✅ Environment validation passed
2025-07-30T02:01:51: ✅ Environment validation passed
2025-07-30T02:01:53: ✅ Environment validation passed
2025-07-30T02:01:54: ✅ Environment validation passed
2025-07-30T02:01:56: ✅ Environment validation passed
2025-07-30T02:01:56: ✅ Environment validation passed
2025-07-30T02:01:57: ✅ Environment validation passed
2025-07-30T02:01:59: ✅ Environment validation passed
2025-07-30T02:02:00: ✅ Environment validation passed
2025-07-30T02:02:01: ✅ Environment validation passed
2025-07-30T02:02:03: ✅ Environment validation passed
2025-07-30T02:02:04: ✅ Environment validation passed
2025-07-30T02:02:05: ✅ Environment validation passed
2025-07-30T02:02:08: ✅ Environment validation passed
2025-07-30T02:02:10: ✅ Environment validation passed
2025-07-30T02:02:14: ✅ Environment validation passed
2025-07-30T02:05:34: ✅ Environment validation passed
2025-07-30T02:05:44: ✅ Environment validation passed
2025-07-30T02:08:20: ✅ Environment validation passed
2025-07-30T02:08:30: ✅ Environment validation passed
2025-07-30T02:12:02: ✅ Environment validation passed
2025-07-30T02:12:12: ✅ Environment validation passed
