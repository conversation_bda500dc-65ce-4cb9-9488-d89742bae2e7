// Routes module
const express = require('express');
const router = express.Router();

// Example routes
router.get('/api/profile', (req, res) => {
  res.json({ message: 'Profile endpoint' });
});

router.get('/api/orders', (req, res) => {
  res.json({ message: 'Orders endpoint' });
});

router.get('/api/orders/:id', (req, res) => {
  const { id } = req.params;
  res.json({ message: `Order ${id} endpoint` });
});

module.exports = router;
